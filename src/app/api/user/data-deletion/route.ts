import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { confirmDeletion, reason } = body

    if (!confirmDeletion) {
      return NextResponse.json(
        { error: 'Deletion confirmation required' },
        { status: 400 }
      )
    }

    // Log the deletion request
    await query(`
      INSERT INTO data_deletion_requests (
        user_id, email, reason, status, created_at
      ) VALUES ($1, $2, $3, 'pending', NOW())
    `, [user.id, user.email, reason || 'User requested account deletion'])

    // For now, we'll mark the account for deletion rather than immediately deleting
    // This allows for a grace period and manual review if needed
    await query(`
      UPDATE users 
      SET 
        deletion_requested_at = NOW(),
        deletion_reason = $2
      WHERE id = $1
    `, [user.id, reason || 'User requested account deletion'])

    return NextResponse.json({
      success: true,
      message: 'Data deletion request submitted successfully',
      details: {
        requestId: user.id,
        status: 'pending',
        estimatedProcessingTime: '30 days',
        gracePeriod: '7 days',
        contact: '<EMAIL>'
      }
    })

  } catch (error) {
    console.error('Error processing data deletion request:', error)
    return NextResponse.json(
      { error: 'Failed to process deletion request' },
      { status: 500 }
    )
  }
}

// Get deletion request status
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user has any pending deletion requests
    const deletionResult = await query(`
      SELECT * FROM data_deletion_requests
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 1
    `, [user.id])

    const userResult = await query(`
      SELECT deletion_requested_at, deletion_reason
      FROM users
      WHERE id = $1
    `, [user.id])

    const userData = userResult.rows[0]
    const latestRequest = deletionResult.rows[0]

    return NextResponse.json({
      hasDeletionRequest: !!userData?.deletion_requested_at,
      deletionRequestedAt: userData?.deletion_requested_at,
      deletionReason: userData?.deletion_reason,
      latestRequest: latestRequest ? {
        id: latestRequest.id,
        status: latestRequest.status,
        reason: latestRequest.reason,
        createdAt: latestRequest.created_at,
        processedAt: latestRequest.processed_at
      } : null,
      gracePeriodInfo: {
        gracePeriodDays: 7,
        estimatedProcessingDays: 30,
        canCancel: !!userData?.deletion_requested_at && !latestRequest?.processed_at
      }
    })
})

// Cancel deletion request
export const DELETE = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Cancel the deletion request
    await query(`
      UPDATE users 
      SET 
        deletion_requested_at = NULL,
        deletion_reason = NULL
      WHERE id = $1
    `, [user.id])

    // Update the deletion request status
    await query(`
      UPDATE data_deletion_requests
      SET 
        status = 'cancelled',
        processed_at = NOW()
      WHERE user_id = $1 AND status = 'pending'
    `, [user.id])

    return NextResponse.json({
      success: true,
      message: 'Data deletion request cancelled successfully'
    })
})

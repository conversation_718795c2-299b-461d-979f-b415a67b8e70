import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getCurrentUser } from '@/lib/auth'
import type { UserBenefitRanking } from '@/types/database'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/user/benefit-rankings - Get current user's benefit rankings
export const GET = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeDetails = searchParams.get('includeDetails') === 'true'

    let sql = `
      SELECT
        ubr.id,
        ubr.user_id,
        ubr.benefit_id,
        ubr.ranking,
        ubr.created_at,
        ubr.updated_at,
        b.name as benefit_name
    `

    if (includeDetails) {
      sql += `,
        b.category_id,
        bc.name as category,
        b.icon,
        b.description,
        b.created_at as benefit_created_at,
        bc.display_name as category_display_name
      `
    }

    sql += `
      FROM user_benefit_rankings ubr
      LEFT JOIN benefits b ON ubr.benefit_id = b.id
    `

    if (includeDetails) {
      sql += `
        LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      `
    }

    sql += `
      WHERE ubr.user_id = $1
      ORDER BY ubr.ranking ASC
    `

    const result = await query(sql, [user.id])

    const rankings = result.rows.map(row => {
      const ranking: UserBenefitRanking & { benefit_name?: string; benefit?: { id: string; name: string; icon?: string; category?: string } } = {
        id: row.id,
        user_id: row.user_id,
        benefit_id: row.benefit_id,
        ranking: row.ranking,
        created_at: row.created_at,
        updated_at: row.updated_at,
        benefit_name: row.benefit_name
      }

      if (includeDetails) {
        ranking.benefit = {
          id: row.benefit_id,
          name: row.benefit_name,
          category_id: row.category_id,
          icon: row.icon,
          description: row.description,
          created_at: row.benefit_created_at,
          category_name: row.category,
          category_display_name: row.category_display_name
        }
      }

      return ranking
    })

    return NextResponse.json({
      rankings,
      total: rankings.length
    })
})

// POST /api/user/benefit-rankings - Create or update user's benefit rankings
export const POST = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    let body
    try {
      body = await request.json()
    } catch (jsonError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    const { rankings } = body

    if (!Array.isArray(rankings)) {
      return NextResponse.json(
        { error: 'Rankings must be an array' },
        { status: 400 }
      )
    }

    // Normalize and validate rankings
    const normalizedRankings = rankings.map((ranking: any) => ({
      benefit_id: ranking.benefit_id || ranking.benefitId,
      ranking: ranking.ranking
    }))

    for (const ranking of normalizedRankings) {
      if (!ranking.benefit_id || typeof ranking.ranking !== 'number') {
        return NextResponse.json(
          { error: 'Each ranking must have benefit_id (or benefitId) and ranking number' },
          { status: 400 }
        )
      }

      if (ranking.ranking < 1 || ranking.ranking > 10) {
        return NextResponse.json(
          { error: 'Ranking must be between 1 and 10' },
          { status: 400 }
        )
      }
    }

    // Check for duplicate rankings
    const rankingValues = normalizedRankings.map(r => r.ranking)
    const uniqueRankings = new Set(rankingValues)
    if (rankingValues.length !== uniqueRankings.size) {
      return NextResponse.json(
        { error: 'Each ranking value must be unique' },
        { status: 400 }
      )
    }

    // Check for duplicate benefit_ids
    const benefitIds = normalizedRankings.map(r => r.benefit_id)
    const uniqueBenefitIds = new Set(benefitIds)
    if (benefitIds.length !== uniqueBenefitIds.size) {
      return NextResponse.json(
        { error: 'Each benefit can only be ranked once' },
        { status: 400 }
      )
    }

    // Verify all benefits exist
    const benefitCheckSql = `
      SELECT id FROM benefits WHERE id = ANY($1)
    `
    const benefitCheckResult = await query(benefitCheckSql, [benefitIds])
    
    if (benefitCheckResult.rows.length !== benefitIds.length) {
      return NextResponse.json(
        { error: 'One or more benefits do not exist' },
        { status: 400 }
      )
    }

    // Begin transaction to update rankings
    await query('BEGIN')

    try {
      // Delete existing rankings for this user
      const _deleteResult = await query(
        'DELETE FROM user_benefit_rankings WHERE user_id = $1',
        [user.id]
      )

      // Insert new rankings using a single query with VALUES clause for better performance
      if (normalizedRankings.length > 0) {
        const values = normalizedRankings.map((_, index) =>
          `($1, $${index * 2 + 2}, $${index * 2 + 3})`
        ).join(', ')

        const params = [user.id]
        normalizedRankings.forEach(ranking => {
          params.push(ranking.benefit_id, ranking.ranking)
        })

        await query(
          `INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
           VALUES ${values}`,
          params
        )
      }

      await query('COMMIT')

      // Get the updated rankings to return
      const updatedRankings = await query(
        `SELECT ubr.ranking, ubr.created_at, ubr.updated_at,
                b.id as benefit_id, b.name as benefit_name, b.icon, b.description
         FROM user_benefit_rankings ubr
         JOIN benefits b ON ubr.benefit_id = b.id
         WHERE ubr.user_id = $1
         ORDER BY ubr.ranking ASC`,
        [user.id]
      )

      return NextResponse.json({
        success: true,
        message: 'Benefit rankings updated successfully',
        count: normalizedRankings.length,
        rankings: updatedRankings.rows
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error updating benefit rankings:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit rankings' },
      { status: 500 }
    )
  }
}

// PUT /api/user/benefit-rankings - Update user's benefit rankings (alias for POST)
export async function PUT(request: NextRequest) {
  return POST(request)
}

// DELETE /api/user/benefit-rankings - Delete all user's benefit rankings
export async function DELETE(_request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const result = await query(
      'DELETE FROM user_benefit_rankings WHERE user_id = $1',
      [user.id]
    )

    return NextResponse.json({
      success: true,
      message: 'All benefit rankings deleted successfully',
      deletedCount: result.rowCount
    })
})

import { NextRequest, NextResponse } from 'next/server'
import { getCompanyLocations, addCompanyLocation, updateCompanyLocation, removeCompanyLocation } from '@/lib/database'
import { requireAdmin } from '@/lib/auth'
import { withLocationNormalization, validateNormalizedLocation } from '@/lib/location-middleware'
import { withErrorHandling } from '@/lib/api-error-handler'
import type { LocationType } from '@/types/database'

export const GET = withErrorHandling(async (
  _request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
  await requireAdmin()
  const { companyId } = await params
  const locations = await getCompanyLocations(companyId)
  return NextResponse.json(locations)
})

export const POST = withErrorHandling(async (
  _request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
  await requireAdmin()
  const { companyId } = await params

  return withLocationNormalization(_request, async (normalizedData) => {
    try {
      const { location_raw, location_type = 'office', is_primary = false, is_headquarters = false } = normalizedData as Record<string, unknown>

      if (!location_raw) {
        return new Response(
          JSON.stringify({ error: 'Location is required' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
      }

      // Validate that location was properly normalized
      if (!validateNormalizedLocation(normalizedData)) {
        return new Response(
          JSON.stringify({ error: 'Could not normalize location. Please check the location format.' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
      }

      const location = await addCompanyLocation(
        companyId,
        normalizedData.location_raw,
        location_type as LocationType,
        Boolean(is_primary),
        Boolean(is_headquarters),
        normalizedData.location_normalized,
        normalizedData.city,
        normalizedData.country,
        normalizedData.country_code,
        normalizedData.latitude,
        normalizedData.longitude
      )

      return new Response(JSON.stringify(location), {
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (error) {
      console.error('Error adding company location:', error)
      return new Response(
        JSON.stringify({ error: 'Failed to add company location' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  })
}

export async function PATCH(
  _request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  await requireAdmin()
  const { companyId: _companyId } = await params

  return withLocationNormalization(_request, async (normalizedData) => {
    try {
      const { location_id, ...updateData } = normalizedData as Record<string, unknown>

      if (!location_id) {
        return new Response(
          JSON.stringify({ error: 'Location ID is required' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
      }

      // If location_raw was provided and normalized, include normalized data
      if (updateData.location_raw && validateNormalizedLocation(normalizedData)) {
        updateData.location_normalized = normalizedData.location_normalized
        updateData.city = normalizedData.city
        updateData.country = normalizedData.country
        updateData.country_code = normalizedData.country_code
        updateData.latitude = normalizedData.latitude
        updateData.longitude = normalizedData.longitude
      }

      const location = await updateCompanyLocation(String(location_id), updateData)
      return new Response(JSON.stringify(location), {
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (error) {
      console.error('Error updating company location:', error)
      return new Response(
        JSON.stringify({ error: 'Failed to update company location' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin()
    const { companyId: _companyId } = await params
    
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get('location_id')

    if (!locationId) {
      return NextResponse.json(
        { error: 'Location ID is required' },
        { status: 400 }
      )
    }

    await removeCompanyLocation(locationId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error removing company location:', error)
    return NextResponse.json(
      { error: 'Failed to remove company location' },
      { status: 500 }
    )
  }
}

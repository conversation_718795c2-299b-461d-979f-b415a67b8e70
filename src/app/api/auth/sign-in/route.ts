import { NextRequest, NextResponse } from 'next/server'
import { createSignInMagicLink, checkRateLimit, createSignInMagicLinkEmail } from '@/lib/magic-link-auth'
import { sendEmail } from '@/lib/email'
import { logSignInRequest, logSignInFailure, logRateLimitHit } from '@/lib/auth-logger'
import { getRequestContext } from '@/lib/logger'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  const requestContext = getRequestContext(request)
    const body = await request.json()
    const { email } = body

    console.log('🔐 Magic link sign in request:', { email })

    if (!email) {
      await logSignInFailure(
        email || 'unknown',
        'missing_email',
        'Email is required',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      await logSignInFailure(
        email,
        'invalid_email',
        'Invalid email format',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Check rate limit
    const isAllowed = await checkRateLimit(email)
    if (!isAllowed) {
      await logRateLimitHit(
        email,
        'sign_in_request',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    try {
      // Create magic link token
      const token = await createSignInMagicLink(email)

      // Send magic link email
      const emailOptions = createSignInMagicLinkEmail(email, token)
      await sendEmail(emailOptions)

      console.log('✅ Magic link sent:', email)

      // Log successful sign-in request
      await logSignInRequest(email, requestContext.ip, requestContext.userAgent)

      return NextResponse.json({
        success: true,
        message: 'Magic link sent! Please check your email and click the link to sign in.'
      })

    } catch (error) {
      if (error instanceof Error && error.message === 'No account found with this email address') {
        await logSignInFailure(
          email,
          'user_not_found',
          error.message,
          requestContext.ip,
          requestContext.userAgent
        )
        return NextResponse.json(
          { error: 'No account found with this email address. Please sign up first.' },
          { status: 404 }
        )
      }

      // Log other errors
      await logSignInFailure(
        email,
        'unknown_error',
        error instanceof Error ? error.message : 'Unknown error',
        requestContext.ip,
        requestContext.userAgent,
        { errorStack: error instanceof Error ? error.stack : undefined }
      )
      throw error
    }

  } catch (error) {
    console.error('Magic link sign in error:', error)

    // Log the outer catch error if we have an email
    const body = await request.json().catch(() => ({}))
    if (body.email) {
      await logSignInFailure(
        body.email,
        'system_error',
        error instanceof Error ? error.message : 'Failed to send magic link',
        requestContext.ip,
        requestContext.userAgent,
        { errorStack: error instanceof Error ? error.stack : undefined }
      )
    }

    return NextResponse.json(
      { error: 'Failed to send magic link' },
      { status: 500 }
    )
})
